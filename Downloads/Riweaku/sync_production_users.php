<?php

/**
 * Enhanced Production User Synchronization Script
 * 
 * This script safely synchronizes user data from production to local database
 * while preserving all local development work on non-user tables.
 * 
 * Features:
 * - Backup creation before any changes
 * - Dry-run mode for safe testing
 * - Only affects user-related data
 * - Handles conflicts intelligently
 * - Comprehensive logging and verification
 * 
 * Usage: php sync_production_users.php [--live] [--update-existing]
 */

// Bootstrap Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use App\Models\User;
use Carbon\Carbon;

class ProductionUserSynchronizer
{
    private $productionSqlFile;
    private $dryRun;
    private $updateExisting;
    private $logFile;
    private $backupFile;
    
    // User-related tables that we'll sync
    private $userTables = [
        'users',
        'agents',
        'agent_achievements', 
        'agent_rewards',
        'api_keys',
        'user_roles',
        'model_has_roles',
        'model_has_permissions'
    ];
    
    public function __construct($productionSqlFile, $dryRun = true, $updateExisting = false)
    {
        $this->productionSqlFile = $productionSqlFile;
        $this->dryRun = $dryRun;
        $this->updateExisting = $updateExisting;
        $this->logFile = __DIR__ . '/user_sync_log_' . date('Y-m-d_H-i-s') . '.txt';
        $this->backupFile = __DIR__ . '/backup_before_sync_' . date('Y-m-d_H-i-s') . '.sql';
    }
    
    public function log($message, $level = 'INFO')
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        echo $logMessage;
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
    }
    
    public function createBackup()
    {
        if ($this->dryRun) {
            $this->log("DRY RUN: Would create backup at {$this->backupFile}");
            return true;
        }
        
        $this->log("Creating database backup...");
        
        $dbHost = env('DB_HOST', '127.0.0.1');
        $dbPort = env('DB_PORT', '3306');
        $dbName = env('DB_DATABASE');
        $dbUser = env('DB_USERNAME');
        $dbPass = env('DB_PASSWORD');
        
        // Use the configured mysqldump path
        $mysqldumpPath = env('MYSQLDUMP_PATH', 'mysqldump');
        
        $command = sprintf(
            '"%s" -h%s -P%s -u%s -p%s %s > "%s"',
            $mysqldumpPath,
            $dbHost,
            $dbPort,
            $dbUser,
            $dbPass,
            $dbName,
            $this->backupFile
        );
        
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception("Failed to create database backup. Return code: {$returnCode}");
        }
        
        if (!file_exists($this->backupFile) || filesize($this->backupFile) < 1000) {
            throw new Exception("Backup file was not created properly or is too small");
        }
        
        $this->log("Backup created successfully: {$this->backupFile}");
        return true;
    }
    
    public function analyzeCurrentState()
    {
        $this->log("Analyzing current database state...");
        
        // Get current user count
        $localUserCount = User::count();
        $this->log("Local database has {$localUserCount} users");
        
        // Get sample of existing emails
        $existingEmails = User::pluck('email')->take(10);
        $this->log("Sample existing emails: " . $existingEmails->implode(', '));
        
        // Check user-related tables
        foreach ($this->userTables as $table) {
            try {
                $count = DB::table($table)->count();
                $this->log("Table '{$table}': {$count} records");
            } catch (Exception $e) {
                $this->log("Table '{$table}': Not found or error - " . $e->getMessage(), 'WARN');
            }
        }
        
        return [
            'local_user_count' => $localUserCount,
            'existing_emails' => User::pluck('email')->toArray()
        ];
    }
    
    public function extractProductionUsers()
    {
        $this->log("Extracting users from production SQL file...");

        if (!file_exists($this->productionSqlFile)) {
            throw new Exception("Production SQL file not found: {$this->productionSqlFile}");
        }

        $sqlContent = file_get_contents($this->productionSqlFile);
        if (!$sqlContent) {
            throw new Exception("Could not read production SQL file");
        }

        // Extract ALL INSERT statements for users table with their column definitions
        $pattern = '/INSERT INTO `users`\s*\((.*?)\)\s*VALUES\s*(.*?);/s';
        preg_match_all($pattern, $sqlContent, $matches, PREG_SET_ORDER);

        if (empty($matches)) {
            throw new Exception("Could not find users INSERT statements in SQL file");
        }

        $allUsers = [];

        // Process each INSERT statement
        foreach ($matches as $match) {
            $columnString = $match[1];
            $valuesString = $match[2];

            // Parse column names
            $columns = $this->parseColumnNames($columnString);

            // Parse user records with column mapping
            $users = $this->parseUserRecords($valuesString, $columns);
            $allUsers = array_merge($allUsers, $users);
        }

        $this->log("Extracted " . count($allUsers) . " users from production database");
        return $allUsers;
    }

    private function parseColumnNames($columnString)
    {
        // Remove backticks and split by comma
        $columnString = str_replace('`', '', $columnString);
        $columns = array_map('trim', explode(',', $columnString));
        return $columns;
    }

    private function parseUserRecords($valuesString, $columns = null)
    {
        $users = [];

        // Default column order if not provided (for backward compatibility)
        if ($columns === null) {
            $columns = [
                'id', 'first_name', 'last_name', 'name', 'email', 'phone_number', 'gender',
                'email_verified_at', 'profile_photo_path', 'password', 'two_factor_secret',
                'two_factor_recovery_codes', 'two_factor_enabled', 'rupa_id', 'wallet_balance',
                'preferred_currency', 'notification_method', 'card_style', 'card_active',
                'card_views', 'card_last_viewed_at', 'card_issued_at', 'card_expires_at',
                'remember_token', 'created_by', 'created_at', 'updated_at', 'bvn',
                'bvn_verified', 'bvn_verified_at', 'bvn_verification_data'
            ];
        }
        
        // Split by lines and look for the actual INSERT data
        $lines = explode("\n", $valuesString);
        $currentRecord = '';
        $inRecord = false;
        $parenCount = 0;
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            // Count parentheses to track record boundaries
            $openParens = substr_count($line, '(');
            $closeParens = substr_count($line, ')');
            
            if ($openParens > 0 && !$inRecord) {
                $inRecord = true;
                $currentRecord = $line;
                $parenCount = $openParens - $closeParens;
            } elseif ($inRecord) {
                $currentRecord .= ' ' . $line;
                $parenCount += $openParens - $closeParens;
            }
            
            // If we've closed all parentheses, we have a complete record
            if ($inRecord && $parenCount <= 0) {
                $user = $this->parseUserRecord($currentRecord, $columns);
                if ($user) {
                    $users[] = $user;
                }
                $inRecord = false;
                $currentRecord = '';
                $parenCount = 0;
            }
        }

        return $users;
    }

    private function parseUserRecord($recordString, $columns)
    {
        // Extract the values between the first ( and last )
        if (preg_match('/\((.*)\)/', $recordString, $matches)) {
            $valuesString = $matches[1];

            // Split by comma but respect quoted strings
            $values = $this->splitValues($valuesString);

            // Map values to columns dynamically
            if (count($values) >= count($columns)) {
                $user = [];

                for ($i = 0; $i < count($columns) && $i < count($values); $i++) {
                    $columnName = $columns[$i];
                    $value = $this->cleanValue($values[$i]);
                    $user[$columnName] = $value;
                }

                return $user;
            }
        }

        return null;
    }
    
    private function splitValues($valuesString)
    {
        $values = [];
        $current = '';
        $inQuotes = false;
        $quoteChar = '';
        $parenDepth = 0;
        
        for ($i = 0; $i < strlen($valuesString); $i++) {
            $char = $valuesString[$i];
            
            if (!$inQuotes && ($char === '"' || $char === "'")) {
                $inQuotes = true;
                $quoteChar = $char;
                $current .= $char;
            } elseif ($inQuotes && $char === $quoteChar && ($i === 0 || $valuesString[$i-1] !== '\\')) {
                $inQuotes = false;
                $current .= $char;
            } elseif (!$inQuotes && $char === '(') {
                $parenDepth++;
                $current .= $char;
            } elseif (!$inQuotes && $char === ')') {
                $parenDepth--;
                $current .= $char;
            } elseif (!$inQuotes && $char === ',' && $parenDepth === 0) {
                $values[] = trim($current);
                $current = '';
            } else {
                $current .= $char;
            }
        }
        
        if ($current !== '') {
            $values[] = trim($current);
        }
        
        return $values;
    }
    
    private function cleanValue($value)
    {
        $value = trim($value);

        if ($value === 'NULL') {
            return null;
        }

        // Remove quotes
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }

        // Unescape quotes
        $value = str_replace(['\\"', "\\'"], ['"', "'"], $value);

        return $value;
    }

    public function filterAndValidateUsers($productionUsers, $existingEmails)
    {
        $this->log("Filtering and validating users...");

        $newUsers = [];
        $updateUsers = [];
        $skippedUsers = [];
        $invalidUsers = [];

        foreach ($productionUsers as $user) {
            // Validate required fields - be more lenient
            if (empty($user['email'])) {
                $invalidUsers[] = [
                    'email' => $user['email'] ?? 'unknown',
                    'reason' => 'Missing email'
                ];
                continue;
            }

            // Validate email format
            if (!filter_var($user['email'], FILTER_VALIDATE_EMAIL)) {
                $invalidUsers[] = [
                    'email' => $user['email'],
                    'reason' => 'Invalid email format'
                ];
                continue;
            }

            // Ensure we have at least a name or first_name/last_name
            if (empty($user['name']) && empty($user['first_name']) && empty($user['last_name'])) {
                $invalidUsers[] = [
                    'email' => $user['email'],
                    'reason' => 'Missing name fields'
                ];
                continue;
            }

            // Generate name if missing
            if (empty($user['name'])) {
                $user['name'] = trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''));
            }

            // Generate password if missing (should not happen in production, but just in case)
            if (empty($user['password'])) {
                $user['password'] = '$2y$12$' . str_repeat('x', 50); // Dummy hash that won't work for login
            }

            $email = strtolower($user['email']);

            if (in_array($email, array_map('strtolower', $existingEmails))) {
                if ($this->updateExisting) {
                    $updateUsers[] = $user;
                } else {
                    $skippedUsers[] = $user['email'] . ' (already exists)';
                }
            } else {
                // Remove the original ID to let auto-increment handle it
                unset($user['id']);
                $newUsers[] = $user;
            }
        }

        $this->log("Found " . count($newUsers) . " new users to add");
        $this->log("Found " . count($updateUsers) . " existing users to update");
        $this->log("Skipped " . count($skippedUsers) . " users");
        $this->log("Invalid " . count($invalidUsers) . " users");

        if (!empty($invalidUsers)) {
            $this->log("Invalid users:", 'WARN');
            foreach (array_slice($invalidUsers, 0, 5) as $invalid) {
                $this->log("  - {$invalid['email']}: {$invalid['reason']}", 'WARN');
            }
        }

        return [
            'new' => $newUsers,
            'update' => $updateUsers,
            'skipped' => $skippedUsers,
            'invalid' => $invalidUsers
        ];
    }

    public function synchronizeUsers($filteredUsers)
    {
        $newUsers = $filteredUsers['new'];
        $updateUsers = $filteredUsers['update'];

        if ($this->dryRun) {
            $this->log("DRY RUN MODE - No actual database changes will be made");
            $this->log("Would add " . count($newUsers) . " new users");
            $this->log("Would update " . count($updateUsers) . " existing users");

            // Show sample of users that would be processed
            foreach (array_slice($newUsers, 0, 5) as $user) {
                $this->log("  - Would add: {$user['name']} ({$user['email']})");
            }

            foreach (array_slice($updateUsers, 0, 3) as $user) {
                $this->log("  - Would update: {$user['name']} ({$user['email']})");
            }

            return [
                'added' => 0,
                'updated' => 0,
                'errors' => 0
            ];
        }

        $this->log("Starting user synchronization...");

        DB::beginTransaction();

        try {
            $addedCount = 0;
            $updatedCount = 0;
            $errorCount = 0;

            // Add new users
            foreach ($newUsers as $user) {
                try {
                    $userData = $this->prepareUserData($user);
                    User::create($userData);
                    $addedCount++;

                    if ($addedCount % 25 === 0) {
                        $this->log("Added {$addedCount} users...");
                    }

                } catch (Exception $e) {
                    $errorCount++;
                    $this->log("Error adding user {$user['email']}: " . $e->getMessage(), 'ERROR');
                }
            }

            // Update existing users if requested
            if ($this->updateExisting) {
                foreach ($updateUsers as $user) {
                    try {
                        $userData = $this->prepareUserData($user);
                        $existingUser = User::where('email', $user['email'])->first();

                        if ($existingUser) {
                            // Only update specific fields, preserve local changes
                            $fieldsToUpdate = [
                                'first_name', 'last_name', 'name', 'phone_number',
                                'gender', 'wallet_balance', 'bvn', 'bvn_verified'
                            ];

                            foreach ($fieldsToUpdate as $field) {
                                if (isset($userData[$field]) && $userData[$field] !== null) {
                                    $existingUser->$field = $userData[$field];
                                }
                            }

                            $existingUser->save();
                            $updatedCount++;
                        }

                        if ($updatedCount % 25 === 0) {
                            $this->log("Updated {$updatedCount} users...");
                        }

                    } catch (Exception $e) {
                        $errorCount++;
                        $this->log("Error updating user {$user['email']}: " . $e->getMessage(), 'ERROR');
                    }
                }
            }

            DB::commit();

            $this->log("User synchronization completed successfully!");
            $this->log("Added: {$addedCount} users");
            $this->log("Updated: {$updatedCount} users");
            $this->log("Errors: {$errorCount} users");

            return [
                'added' => $addedCount,
                'updated' => $updatedCount,
                'errors' => $errorCount
            ];

        } catch (Exception $e) {
            DB::rollBack();
            $this->log("Error during synchronization: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }

    private function prepareUserData($user)
    {
        // Convert string booleans to actual booleans
        $booleanFields = ['two_factor_enabled', 'card_active', 'bvn_verified'];
        foreach ($booleanFields as $field) {
            if (isset($user[$field])) {
                $user[$field] = in_array($user[$field], [1, '1', true], true);
            }
        }

        // Convert numeric fields
        $numericFields = ['wallet_balance', 'card_views'];
        foreach ($numericFields as $field) {
            if (isset($user[$field]) && $user[$field] !== null) {
                $user[$field] = is_numeric($user[$field]) ? (float)$user[$field] : 0;
            }
        }

        // Handle JSON fields
        $jsonFields = ['two_factor_recovery_codes', 'bvn_verification_data'];
        foreach ($jsonFields as $field) {
            if (isset($user[$field]) && $user[$field] !== null && $user[$field] !== '') {
                if (!is_array($user[$field])) {
                    $decoded = json_decode($user[$field], true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $user[$field] = $decoded;
                    } else {
                        $user[$field] = null;
                    }
                }
            }
        }

        // Handle datetime fields
        $dateFields = ['email_verified_at', 'card_last_viewed_at', 'card_issued_at', 'card_expires_at', 'created_at', 'updated_at', 'bvn_verified_at'];
        foreach ($dateFields as $field) {
            if (isset($user[$field]) && $user[$field] !== null && $user[$field] !== '') {
                try {
                    $user[$field] = Carbon::parse($user[$field]);
                } catch (Exception $e) {
                    $user[$field] = null;
                }
            }
        }

        // Generate unique rupa_id if missing or duplicate
        if (empty($user['rupa_id'])) {
            $user['rupa_id'] = 'rp-' . strtolower(substr(md5(uniqid()), 0, 6));
        }

        // Remove any fields that don't exist in the local schema
        $allowedFields = [
            'first_name', 'last_name', 'name', 'email', 'phone_number', 'gender',
            'email_verified_at', 'profile_photo_path', 'password', 'two_factor_secret',
            'two_factor_recovery_codes', 'two_factor_enabled', 'rupa_id', 'wallet_balance',
            'preferred_currency', 'notification_method', 'card_style', 'card_active',
            'card_views', 'card_last_viewed_at', 'card_issued_at', 'card_expires_at',
            'remember_token', 'created_by', 'created_at', 'updated_at', 'bvn',
            'bvn_verified', 'bvn_verified_at', 'bvn_verification_data'
        ];

        return array_intersect_key($user, array_flip($allowedFields));
    }

    public function verifyResults($results)
    {
        $this->log("Verifying synchronization results...");

        // Check total user count
        $totalUsers = User::count();
        $this->log("Total users in database: {$totalUsers}");

        // Check for duplicate emails
        $duplicateEmails = DB::table('users')
            ->select('email', DB::raw('COUNT(*) as count'))
            ->groupBy('email')
            ->having('count', '>', 1)
            ->get();

        if ($duplicateEmails->count() > 0) {
            $this->log("WARNING: Found duplicate emails:", 'WARN');
            foreach ($duplicateEmails as $duplicate) {
                $this->log("  - {$duplicate->email}: {$duplicate->count} occurrences", 'WARN');
            }
        } else {
            $this->log("✓ No duplicate emails found");
        }

        // Check recent additions
        $recentUsers = User::where('created_at', '>=', Carbon::today())
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get(['name', 'email', 'created_at']);

        if ($recentUsers->count() > 0) {
            $this->log("Recent user additions:");
            foreach ($recentUsers as $user) {
                $this->log("  - {$user->name} ({$user->email}) - {$user->created_at}");
            }
        }

        return [
            'total_users' => $totalUsers,
            'duplicate_emails' => $duplicateEmails->count(),
            'recent_additions' => $recentUsers->count()
        ];
    }

    public function run()
    {
        try {
            $this->log("=== Production User Synchronization Started ===");
            $this->log("Mode: " . ($this->dryRun ? "DRY RUN" : "LIVE"));
            $this->log("Update existing: " . ($this->updateExisting ? "YES" : "NO"));
            $this->log("Production SQL file: " . $this->productionSqlFile);
            $this->log("Log file: " . $this->logFile);

            // Step 1: Create backup
            $this->createBackup();

            // Step 2: Analyze current state
            $currentState = $this->analyzeCurrentState();

            // Step 3: Extract production users
            $productionUsers = $this->extractProductionUsers();

            // Step 4: Filter and validate users
            $filteredUsers = $this->filterAndValidateUsers($productionUsers, $currentState['existing_emails']);

            // Step 5: Synchronize users
            $results = $this->synchronizeUsers($filteredUsers);

            // Step 6: Verify results
            if (!$this->dryRun) {
                $verification = $this->verifyResults($results);
            }

            $this->log("=== Synchronization Completed Successfully ===");
            $this->log("Summary:");
            $this->log("  - New users added: " . $results['added']);
            $this->log("  - Existing users updated: " . $results['updated']);
            $this->log("  - Errors encountered: " . $results['errors']);
            $this->log("  - Log file: " . $this->logFile);

            if (!$this->dryRun && isset($this->backupFile)) {
                $this->log("  - Backup file: " . $this->backupFile);
            }

        } catch (Exception $e) {
            $this->log("FATAL ERROR: " . $e->getMessage(), 'ERROR');
            $this->log("Stack trace: " . $e->getTraceAsString(), 'ERROR');
            throw $e;
        }
    }
}

// CLI Usage
if (php_sapi_name() === 'cli') {
    $productionSqlFile = __DIR__ . '/avenuepl_riwedbase (1).sql';
    $dryRun = true;
    $updateExisting = false;

    // Parse command line arguments
    foreach ($argv as $arg) {
        if ($arg === '--live') {
            $dryRun = false;
        }
        if ($arg === '--update-existing') {
            $updateExisting = true;
        }
        if (strpos($arg, '--file=') === 0) {
            $productionSqlFile = substr($arg, 7);
        }
    }

    if (!file_exists($productionSqlFile)) {
        echo "Error: Production SQL file not found at {$productionSqlFile}\n";
        echo "Usage: php sync_production_users.php [--live] [--update-existing] [--file=path/to/file.sql]\n";
        exit(1);
    }

    echo "Enhanced Production User Synchronization\n";
    echo "=======================================\n";
    echo "Mode: " . ($dryRun ? "DRY RUN (use --live to execute)" : "LIVE EXECUTION") . "\n";
    echo "Update existing: " . ($updateExisting ? "YES" : "NO (use --update-existing to enable)") . "\n";
    echo "SQL File: {$productionSqlFile}\n\n";

    if (!$dryRun) {
        echo "⚠️  WARNING: This will modify your database!\n";
        echo "Make sure you have a backup before proceeding.\n";
        echo "Are you sure you want to continue? (yes/no): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);

        if (trim(strtolower($line)) !== 'yes') {
            echo "Operation cancelled.\n";
            exit(0);
        }
    }

    echo "Starting synchronization...\n\n";

    try {
        $synchronizer = new ProductionUserSynchronizer($productionSqlFile, $dryRun, $updateExisting);
        $synchronizer->run();

        echo "\n✅ Synchronization completed successfully!\n";

    } catch (Exception $e) {
        echo "\n❌ Synchronization failed: " . $e->getMessage() . "\n";
        exit(1);
    }
}
