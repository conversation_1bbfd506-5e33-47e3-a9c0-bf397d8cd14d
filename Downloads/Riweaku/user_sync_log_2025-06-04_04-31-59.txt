[2025-06-04 04:31:59] [INFO] === Production User Synchronization Started ===
[2025-06-04 04:31:59] [INFO] Mode: DRY RUN
[2025-06-04 04:31:59] [INFO] Update existing: NO
[2025-06-04 04:31:59] [INFO] Production SQL file: /Users/<USER>/Downloads/Riweaku/avenuepl_riwedbase (1).sql
[2025-06-04 04:31:59] [INFO] Log file: /Users/<USER>/Downloads/Riweaku/user_sync_log_2025-06-04_04-31-59.txt
[2025-06-04 04:31:59] [INFO] DRY RUN: Would create backup at /Users/<USER>/Downloads/Riweaku/backup_before_sync_2025-06-04_04-31-59.sql
[2025-06-04 04:31:59] [INFO] Analyzing current database state...
[2025-06-04 04:31:59] [INFO] Local database has 230 users
[2025-06-04 04:31:59] [INFO] Sample existing emails: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
[2025-06-04 04:31:59] [INFO] Table 'users': 230 records
[2025-06-04 04:31:59] [INFO] Table 'agents': 0 records
[2025-06-04 04:31:59] [INFO] Table 'agent_achievements': 7 records
[2025-06-04 04:31:59] [INFO] Table 'agent_rewards': 2 records
[2025-06-04 04:31:59] [INFO] Table 'api_keys': 1 records
[2025-06-04 04:31:59] [WARN] Table 'user_roles': Not found or error - SQLSTATE[42S02]: Base table or view not found: 1146 Table 'riwe_insurtech.user_roles' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `user_roles`)
[2025-06-04 04:31:59] [INFO] Table 'model_has_roles': 231 records
[2025-06-04 04:31:59] [INFO] Table 'model_has_permissions': 3 records
[2025-06-04 04:31:59] [INFO] Extracting users from production SQL file...
[2025-06-04 04:31:59] [INFO] Extracted 633 users from production database
[2025-06-04 04:31:59] [INFO] Filtering and validating users...
[2025-06-04 04:31:59] [INFO] Found 411 new users to add
[2025-06-04 04:31:59] [INFO] Found 0 existing users to update
[2025-06-04 04:31:59] [INFO] Skipped 89 users
[2025-06-04 04:31:59] [INFO] Invalid 133 users
[2025-06-04 04:31:59] [WARN] Invalid users:
[2025-06-04 04:31:59] [WARN]   - Admin: Invalid email format
[2025-06-04 04:31:59] [WARN]   - Chigozirim Izy: Invalid email format
[2025-06-04 04:31:59] [WARN]   - Chigozir EPIC: Invalid email format
[2025-06-04 04:31:59] [WARN]   - JD: Invalid email format
[2025-06-04 04:31:59] [WARN]   - JD Digital: Invalid email format
[2025-06-04 04:31:59] [INFO] DRY RUN MODE - No actual database changes will be made
[2025-06-04 04:31:59] [INFO] Would add 411 new users
[2025-06-04 04:31:59] [INFO] Would update 0 existing users
[2025-06-04 04:31:59] [INFO]   - Would add: Yanmelu Emmanuel Yateghtegh (<EMAIL>)
[2025-06-04 04:31:59] [INFO]   - Would add: Ude Iorliam Clement (<EMAIL>)
[2025-06-04 04:31:59] [INFO]   - Would add: Enngo Innocent Iorlumun (<EMAIL>)
[2025-06-04 04:31:59] [INFO]   - Would add: Tor Justine Mnongo Mtom (<EMAIL>)
[2025-06-04 04:31:59] [INFO]   - Would add: Atsadu Gabriel Aondokaa (<EMAIL>)
[2025-06-04 04:31:59] [INFO] === Synchronization Completed Successfully ===
[2025-06-04 04:31:59] [INFO] Summary:
[2025-06-04 04:31:59] [INFO]   - New users added: 0
[2025-06-04 04:31:59] [INFO]   - Existing users updated: 0
[2025-06-04 04:31:59] [INFO]   - Errors encountered: 0
[2025-06-04 04:31:59] [INFO]   - Log file: /Users/<USER>/Downloads/Riweaku/user_sync_log_2025-06-04_04-31-59.txt
